import { useRef } from 'react';
import { Track } from 'livekit-client';

// PiP functionality as a custom hook
export const usePictureInPicture = (
  room,
  tracks,
  isTrackReference,
  generateAvatar,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast
) => {
  const pipWindowRef = useRef(null);

  // Function to update PiP content when tracks change
  const updatePipContent = () => {
    console.log('updatePipContent called');
    
    if (!pipWindowRef.current) {
      console.log('No PiP window ref');
      return;
    }

    const pipDoc = pipWindowRef.current.document;
    const pipContent = pipDoc.getElementById('pip-content');
    if (!pipContent) {
      console.log('No PiP content element found');
      return;
    }

    console.log('PiP content element found, tracks length:', tracks.length);

    // Find current track to show
    let trackToShow = null;

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      trackToShow = screenShareTracks[0];
      console.log('PiP: Showing screen share from', trackToShow.participant.name);
    } else {
      // Priority 2: Local camera
      const localCameraTracks = tracks
        .filter(isTrackReference)
        .filter((track) => 
          track.publication.source === Track.Source.Camera && 
          track.participant.isLocal
        );
      
      console.log('Local camera tracks found:', localCameraTracks.length);
      
      if (localCameraTracks.length > 0) {
        trackToShow = localCameraTracks[0];
        console.log('PiP: Showing local camera, muted:', trackToShow.publication.isMuted);
      }
    }

    // If no track, create dummy for avatar
    if (!trackToShow) {
      trackToShow = {
        participant: room.localParticipant,
        source: Track.Source.Camera,
        publication: null
      };
      console.log('PiP: Showing avatar for', trackToShow.participant.name);
    }

    console.log('Final track to show:', {
      source: trackToShow.source,
      hasPublication: !!trackToShow.publication,
      participantName: trackToShow.participant.name
    });

    // Clear existing content
    pipContent.innerHTML = '';

    // Check if we have a video track to show
    if (trackToShow.publication && trackToShow.publication.track && !trackToShow.publication.isMuted) {
      console.log('Showing video track');
      
      // Create video element
      const video = pipDoc.createElement('video');
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.style.width = '100%';
      video.style.height = '100%';
      video.style.objectFit = 'contain';
      video.style.background = '#000';
      
      // Set the MediaStream from the track
      const stream = new MediaStream([trackToShow.publication.track.mediaStreamTrack]);
      video.srcObject = stream;
      
      pipContent.appendChild(video);
      
      // Force play
      video.play().catch(e => console.log('Video play error:', e));
      
      console.log('Video element created and added to PiP');
    } else {
      // Show avatar when no video or camera is off
      console.log('Showing avatar - no video track or camera muted');
      
      const name = room.localParticipant.name || 'You';
      const avatarText = generateAvatar(name);
      
      pipContent.innerHTML = `
        <div style="
          display: flex; 
          align-items: center; 
          justify-content: center; 
          height: 100%; 
          color: white;
          text-align: center;
          background: #1a1a1a;
        ">
          <div>
            <div style="
              width: 80px;
              height: 80px;
              border-radius: 50%;
              background: #555;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 32px;
              font-weight: bold;
              margin: 0 auto 12px;
            ">${avatarText}</div>
            <div style="font-size: 14px;">${name}</div>
            <div style="font-size: 10px; opacity: 0.7; margin-top: 4px;">
              ${trackToShow.publication ? 'Camera Off' : 'No Camera'}
            </div>
          </div>
        </div>
      `;
    }

    console.log('PiP content updated');
  };

  const togglePipMode = async (enabled) => {
    // Check Document PiP support
    if (!('documentPictureInPicture' in window)) {
      setToastNotification("Document Picture-in-Picture not supported");
      setToastStatus("error");
      setShowToast(true);
      return;
    }

    // Close existing PiP
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
      setIsPIPEnabled(false);
      return;
    }

    if (!enabled) return;

    try {
      // Create PiP window with smaller default size
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: 320,
        height: 240,
      });

      pipWindowRef.current = pipWindow;
      setIsPIPEnabled(true);

      // Setup PiP document
      const pipDoc = pipWindow.document;
      
      // Add styles
      pipDoc.head.innerHTML = `
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body { 
            background: #1a1a1a; 
            font-family: Arial, sans-serif; 
            overflow: hidden;
            width: 100vw;
            height: 100vh;
          }
          .pip-container {
            width: 100%;
            height: 100%;
            position: relative;
          }
          .pip-header {
            background: #2c2c2c;
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: bold;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
          }
          .pip-content {
            position: absolute;
            top: 40px;
            left: 0;
            right: 0;
            bottom: 0;
          }
        </style>
      `;

      // Add HTML structure
      pipDoc.body.innerHTML = `
        <div class="pip-container">
          <div class="pip-header">Daakia</div>
          <div class="pip-content" id="pip-content"></div>
        </div>
      `;

      // Initial render - use updatePipContent function
      updatePipContent();

      // Handle window close
      pipWindow.addEventListener('pagehide', () => {
        pipWindowRef.current = null;
        setIsPIPEnabled(false);
      });

    } catch (error) {
      console.error('PiP error:', error);
      setToastNotification("Failed to create Picture-in-Picture");
      setToastStatus("error");
      setShowToast(true);
    }
  };

  return {
    togglePipMode,
    updatePipContent,
    pipWindowRef
  };
};
